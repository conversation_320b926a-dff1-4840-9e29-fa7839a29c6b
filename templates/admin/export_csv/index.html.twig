{% extends 'admin/base.html.twig' %}

{% block title %}{{ ("admin.bilan.titles.default")|trans }}{% endblock %}

{% block body %}


{% macro exportBlock(type, fileStatus, fileLastUpdate, start, end) %}
    <div class="col-lg-6">
        <div class="box box-info">
            <div class="box-header">
                <h3 class="box-title">{{ ("admin.bilan.titles." ~ type)|trans }} {% if type == "collaborators" %} <i>(donnés mises à jour le {{ "now"|date('d/m/Y') }} à 03h00</i>){% endif %} </h3>
            </div>
            <div class="box-body">

                <div class="form-inline">
                    {% if type == "sessionsFullJson" %}
                        <label class="radio-inline">
                            <input type="radio" name="export-radios-{{ type }}" value="creation" checked>Date de création
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="export-radios-{{ type }}" value="edition">Date de modification
                         </label>
                        <br><br>
                    {% endif %}
                    <div class="form-group mrl">
                        <label class="control-label" for="eduprat_search_start_{{ type }}">{% if type == "sessionsFullJson" %}<div id="label-export-json">Date création session du</div>{% elseif type == "commissionnement" or type == "lead_inscrits" %} Date de réception du lead du{% else %}Date réunion du{% endif %}</label>
                        <br>
                        <input type="text" id="eduprat_search_start_{{ type }}" name="eduprat_search[start]" provider="datepicker" class="datepicker form-control form-control" value="01/01/{{ "now"|date("Y") }}" />
                        <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                    </div>
                    <div class="form-group mrl">
                        <label class="control-label" for="eduprat_search_end_{{ type }}">au</label>

                        <br>
                        <input type="text" id="eduprat_search_end_{{ type }}" name="eduprat_search[end]" provider="datepicker" class="datepicker form-control form-control" value="31/12/{{ "now"|date("Y") }}" />
                        <span class="formation input-group-addon"><i class="glyphicon glyphicon-th"></i></span>
                    </div>
                </div>

                <br>

            <div class="input-group input-group-md mbl">
                <a class="btn btn-eduprat {% if fileStatus[type] == 'generating' %} disabled {% endif %} btn-generate-{{ type }} generate-csv" data-type="{{ type }}" data-started="false" data-href="{{ url('bilan_generate_csv_export', { type : type, start: "__start__", end: "__end__", jsonReferenceDate: "_jsonReferenceDate_" }) }}" href="{{ url('bilan_generate_csv_export', { type : type, start: "__start__", end: "__end__" }) }}">{{ ("admin.bilan.generate." ~ type) | trans({"%type%": type}) }}</a>
                {# <a class="btn btn-eduprat {% if fileStatus[type] == 'generating' %} disabled {% endif %}" id="generate-csv generate-csv-{{ type }}" data-type="{{ type }}" data-started="false" data-href="{{ url('bilan_generate_csv_export', { type : type, start: "__start__", end: "__end__" }) }}" href="{{ url('bilan_generate_csv_export', { type : type, start: "__start__", end: "__end__" }) }}">{{ ("admin.bilan.generate." ~ type) | trans({"%type%": type}) }}</a> #}
            </div>

                <div class="download-csv">
                    {% if fileStatus[type] == 'error' %}
                        <span id="info-{{ type }}">{{ "admin.formation.generation_error"|trans }}</span>
                    {% else %}
                        {% if fileStatus[type] == 'generating' %}
                            <span id="info-{{ type }}">{{ "admin.formation.generation"|trans }}</span>
                        {% elseif fileStatus[type] == 'generated' %}
                            {% set startDate = start[type]%}
                            {% set endDate = end[type]%}
                            <span id="info-{{ type }}">
                    <a href="{{ url('csv_export_file_get', {type: type, start: startDate, end: endDate}) }}" target="_blank">{{ "admin.formation.telecharger"|trans }} {{ ('admin.bilan.type.' ~ type)|trans }} ({{ 'admin.bilan.lastUpdate'|trans }} {{ fileLastUpdate[type] }}) (Date de réunion du {{ startDate }} au {{ endDate }})</a>
                </span>
                        {% else %}
                            <span id="info-{{ type }}" class="hidden"></span>
                        {% endif %}
                    {% endif %}
                </div>

            </div>
        </div>
    </div>
{% endmacro %}

<div>
    <ul class="nav nav-tabs">
        <li class="active"><a href="#tab_global" data-toggle="tab" aria-expanded="true">Général</a></li>
        <li><a href="#tab_formation" data-toggle="tab" aria-expanded="false">Formation</a></li>
        <li><a href="#tab_comptabilite" data-toggle="tab" aria-expanded="false">Comptabilité</a></li>
        <li><a href="#tab_qualite" data-toggle="tab" aria-expanded="false">Qualité</a></li>
        <li><a href="#tab_lead" data-toggle="tab" aria-expanded="false">Lead</a></li>
    </ul>
    <div class="tab-content">
        <div class="tab-pane active" id="tab_global">
            <div class="row">
                {{ _self.exportBlock("coordinator", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("supervisor", fileStatus, fileLastUpdate, start, end) }}
            </div>
        </div>
        <div class="tab-pane" id="tab_formation">
            <div class="row">
                {{ _self.exportBlock("sessions", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("sessionsFullJson", fileStatus, fileLastUpdate, start, end) }}
            </div>
            <div class="row">
                {{ _self.exportBlock("participations", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("sessionsFull", fileStatus, fileLastUpdate, start, end) }}
            </div>
            <div class="row">
                {{ _self.exportBlock("finance_sous_mode", fileStatus, fileLastUpdate, start, end) }}
            </div>
        </div>
        <div class="tab-pane" id="tab_comptabilite">
            <div class="row">
                {{ _self.exportBlock("invoices", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("coordinators", fileStatus, fileLastUpdate, start, end) }}
            </div>
            <div class="row">
                {{ _self.exportBlock("former_with_siret", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("former_without_siret", fileStatus, fileLastUpdate, start, end) }}
            </div>
            <div class="row">
                {{ _self.exportBlock("detail_session", fileStatus, fileLastUpdate, start, end) }}
            </div>
        </div>
        <div class="tab-pane" id="tab_qualite">
            <div class="row">
                {{ _self.exportBlock("satisfaction", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("collaborators", fileStatus, fileLastUpdate, start, end) }}
            </div>
            <div class="row">
                {{ _self.exportBlock("etutorat", fileStatus, fileLastUpdate, start, end) }}
            </div>
        </div>
        <div class="tab-pane" id="tab_lead">
            <div class="row">
                {{ _self.exportBlock("commissionnement", fileStatus, fileLastUpdate, start, end) }}
                {{ _self.exportBlock("lead_inscrits", fileStatus, fileLastUpdate, start, end) }}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block javascripts %}
    <script>
		$(document).ready(function() {

			function formatDate(date) {
				var d = new Date(date),
					month = '' + (d.getMonth() + 1),
					day = '' + d.getDate(),
					year = d.getFullYear();

				if (month.length < 2) month = '0' + month;
				if (day.length < 2) day = '0' + day;

				return [year, month, day].join('-');
			}

			$('.generate-csv').click(function (e) {
				var $this = $(this);
				if ($this.data('started') === true) return false;
				e.preventDefault();

                var type = $this.data('type');

                var jsonReferenceDate = null;

				if ($this.closest(".box-body").find("input[type=radio]").length > 0) {
					jsonReferenceDate = $this.closest(".box-body").find("input[type=radio]:checked").val();
                }

				var start = formatDate($("#eduprat_search_start_" + type).data('datepicker').dates[0]);
				var end = formatDate($("#eduprat_search_end_" + type).data('datepicker').dates[0]);

				jQuery.ajax({
					url: $this.data('href').replace('__start__', start).replace('__end__', end).replace('_jsonReferenceDate_', jsonReferenceDate),
					dataType: 'json',
					success: function(data, textStatus, xhr) {
						if (data.status == "ok") {
							$('.btn-generate-' + type).addClass('disabled');
							$('#info-' + type).removeClass('hidden');
							$('#info-' + type).html('La génération de votre document est en cours, il sera bientôt disponible. Réactualisez la page dans environ 2 minutes');
						}
					},
					error: function(xhr, textStatus, errorThrown) {
						console.log(errorThrown);
					}
				});
				$this.data('started', true);
			});

            $('input[name=export-json-radios]').click(function (e) {
                $("#label-export-json").empty();
                if ($('input[name=export-json-radios]:checked').val() == "edition") {
                    $("#label-export-json").append("Date modification session du");
                } else {
                    $("#label-export-json").append("Date création session du");
                }
            });

		});
    </script>
{% endblock %}