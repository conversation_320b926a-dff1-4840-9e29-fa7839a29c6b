{% extends 'audit/base.html.twig' %}

{% block css %}
    <link rel="stylesheet" href="{{ asset('css/audit.css') }}">
    <link rel="stylesheet" href="{{ asset('js/Gallery-2.33.0/css/blueimp-gallery.min.css') }}">
    <link href="{{ asset('css/foundation-components/spaces.css') }}" rel="stylesheet">
{% endblock %}

{% block body %}
    {% include "audit/common/timeline.html.twig" %}

    <hr>
    <h5>{{ "audit.patient" | trans }} n°{{ patient }} / {{ audit.nbPatients }}</h5>
    {% if participation.getDescriptionByPatient(auditId, patient) is not null %}
        {{ participation.getDescriptionByPatient(auditId, patient) }}
    {% endif %}
    {% if participation.formation.isPredefined %}
        {% for patientDescription in participation.formation.audit.patientDescriptions %}
            {% if patientDescription.patient == patient %}
                {{ patientDescription.description|nl2br }}
            {% endif %}
        {% endfor %}
    {% endif %}
    <hr>
    <h4>Vos réponses :</h4>
    <br>
    {% for question in audit.questions %}
        <div class="audit-question">
            <div><b>{{ question.label }}</b></div>
            {% if question.interpretation %}
                <div><p class="postulat">Postulat&nbsp:</p><p>{{ question.interpretation | raw }}</p></div>
                <br>
            {% endif %}
            <div>
                {% if participation.getAuditAnswerByQuestion(question, auditId, patient) is not null %}
                    {{ ("audit.choices." ~ participation.getAuditAnswerByQuestion(question, auditId, patient)) | trans }}
                {% else %}
                    {{ "audit.choices.no_answer" | trans }}
                {% endif %}
            </div>
        </div>
    {% endfor %}
    {% for question in audit.surveyQuestions %}
        {% if question.patient == patient %}
            <div class="audit-question">
            <div><b>{{ question.label }}</b></div>
            <div>
                {% set answer = participation.getAuditAnswerByQuestionForSurvey(question, auditId, patient) %}
                {% if answer is not null %}

                    {% if question.type == "choice" %}
                        {% for choiceAnswer in question.choices %}
                            {% set isAnswer = choiceAnswer.label in answer %}
                            <div class="checkbox">
                                <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>{{ choiceAnswer.label }}</label>
                            </div>
                        {% endfor %}
                        {% set isAnswer = "ne_saispas" in answer %}
                        <div class="checkbox">
                            <label class="{% if isAnswer %}bold{% endif %}"><input type="checkbox" disabled="disabled" {% if isAnswer %}checked="checked"{% endif %}>Je ne sais pas</label>
                        </div>
                    {% elseif question.type == "radio" %}
                        {% if answer == "ne_saispas" %}
                            Je ne sais pas
                        {% elseif answer == 2 %}
                            Non applicable
                        {% elseif answer == 1 %}
                            Oui
                        {% elseif answer == 0 %}
                            Non
                        {% endif %}
                    {% else %}
                        {{ answer | raw }}
                    {% endif %}
                {% else %}
                    {{ "audit.choices.no_answer" | trans }}
                {% endif %}
            </div>
        </div>
        {% endif %}
    {% endfor %}
    <hr>

    <div class="page-footer">
        <div class="text-right">
            <a href="{{ url("eduprat_front_next_module", { participation: participation.id, module: current_module }) }}" class="btn btn-eduprat btn-medium">Suivant <i class="fa-solid fa-chevron-right next-icon"></i></a>
            {% if courseManager.canAccessToModules(participation, app.user) %}
                <a href="{{ url("eduprat_front_next_module", { participation: participation.id, module: current_module, redirect: "admin" }) }}" class="btn btn-eduprat btn-medium">{{ "front.btn.redirect_admin"|trans }}</a>
            {% endif %}
        </div>
        {% if audit.nbPatients == patient %}
            {% set modulesLeftSentence = courseManager.modulesLeftSentence(step, course, participation.formation) %}
            {% if modulesLeftSentence %}
                <div class="text-right font-16">
                {{ modulesLeftSentence|raw }}
                </div>
            {% endif %}
        {% endif %}
    </div>

    <div class="text-center">
        <ul class="pagination">
            {% if audit.nbPatients %}
                {% for patientId in 1..audit.nbPatients  %}
                    <li {% if patientId == patient %}class="active"{% endif %}><a href="{{ url('eduprat_audit_show', {id: participation.id, auditId: auditId, patient: patientId }) }}">{{ patientId }}</a></li>
                {% endfor %}
            {% endif %}
        </ul>
    </div>
{% endblock %}

