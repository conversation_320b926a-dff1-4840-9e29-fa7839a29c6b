<div class="page-footer">
    {% if not isLessonCompleted and isLastActivity and activity.lesson.minDuration and currentTime < (activity.lesson.minDuration * 60) %}<div class="alert alert-danger alert-obligatoire module-notif">Veuillez parcourir les activités de la leçon jusqu'au temps minimum indiqué.</div>{% endif %}
    <div class="text-right">
        {% if activity.lesson.position > 1 %}
            {% set targetLesson = activity.lesson.elearning.getLessonByPosition(activity.lesson.position - 1) %}
            {% if targetLesson.isPresentielle %}
                {% if targetLesson.position != 1 %}
                    <a href="{{ url("eduprat_front_module_elearning_lesson", { lesson: targetLesson.id , participation: participation.id, comeBack: true }) }}" class="btn btn-eduprat btn-medium">Revenir à la leçon précédente</a>
                {% endif %}
            {% else %}
                <a href="{{ url("eduprat_front_module_elearning_lesson", { lesson: targetLesson.id , participation: participation.id, comeBack: true }) }}" class="btn btn-eduprat btn-medium">Revenir à la leçon précédente</a>
            {% endif %}
        {% endif %}
        {# <button type="submit" href="{{ url('eduprat_front_module_elearning_activity', { activity : activity.id, participation: participation.id }) }}" class="btn btn-eduprat btn-medium"> #}
        {% if activity.isQuiz %}
            <button id="submit-activity" type="submit" href="{{ url('eduprat_front_module_elearning_activity', { activity : activity.id, participation: participation.id }) }}" class="btn btn-eduprat btn-medium">
                {% if question.hasEmptyExpectedAnswerForText() %} Valider la leçon {% else %} Répondre {% endif %}
            </button>
        {% elseif activity.isPdf %}
            {% if not activity.isUndownloadablePdf %}
                <a id="download-file" target="_blank" href="{{ url("eduprat_activity_pdf_file", { activity: activity.id }) }}" class="btn btn-eduprat btn-medium download-file btn-secondaire">Télécharger le PDF</a>
            {% endif %}
            <button id="submit-activity" {% if not isLessonCompleted and isLastActivity and activity.lesson.minDuration and currentTime < (activity.lesson.minDuration * 60) %} disabled {% endif %} type="submit" href="{{ url('eduprat_front_module_elearning_activity', { activity : activity.id, participation: participation.id }) }}" class="btn btn-eduprat btn-medium">
                {% if isLastActivity %} {% if isLessonCompleted %} Passer à la leçon suivante {% else %} Valider la leçon {% endif %} {% else %} Passez à l'activité suivante {% endif %}
            </button>
        {% else %}
            <button id="submit-activity" {% if not isLessonCompleted and isLastActivity and activity.lesson.minDuration and currentTime < (activity.lesson.minDuration * 60) %} disabled {% endif %} type="submit" href="{{ url('eduprat_front_module_elearning_activity', { activity : activity.id, participation: participation.id }) }}" class="btn btn-eduprat btn-medium">
                {% if isLastActivity %} {% if isLessonCompleted %} Passer à la leçon suivante {% else %} Valider la leçon {% endif %} {% else %} Passez à l'activité suivante {% endif %}
            </button>
        {% endif %}
    </div>
</div>
