<?php

namespace Eduprat\AdminBundle\Form;

use Alienor\FormBundle\Form\AbstractBaseType;
use Ed<PERSON>rat\DomainBundle\Entity\DownloadedPlaquetteFile;
use Symfony\Component\Form\Extension\Core\Type\SubmitType;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DownloadedPlaquetteFileType extends AbstractBaseType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('originalName', TextType::class, array(
                'required' => true,
            ))
            ->add('titre', TextType::class, array(
                'required' => false,
                'attr' => [
                    'maxlength' => 20,
                ]
            ))
            ->add('telecharger', SubmitType::class, [
                'label' => 'admin.global.download',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void {
        $resolver->setDefaults(array(
            'data_class' => DownloadedPlaquetteFile::class,
        ));
    }

    public function getBlockPrefix(): string {
        return 'eduprat_download_file';
    }
}
