<?php

namespace Eduprat\DomainBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Ed<PERSON>rat\AdminBundle\Entity\Person;
use Eduprat\DomainBundle\Entity\Formation;
use Eduprat\DomainBundle\Entity\Indemnisation;
use Ed<PERSON>rat\DomainBundle\Entity\IndemnisationPriseEnCharge;
use Eduprat\DomainBundle\Entity\Programme;
use Eduprat\DomainBundle\Entity\SelectionFormation;
use Eduprat\DomainBundle\Form\ProgrammeType;
use Eduprat\DomainBundle\Model\PlaquetteSearch;
use Eduprat\DomainBundle\Repository\FormationRepository;
use Symfony\Bundle\SecurityBundle\Security;
use Symfony\Component\Security\Core\User\UserInterface;

class PlaquetteManager
{
    const INCLUDE_ALL = 0;
    const EXCLUDE_ALL = 1;
    const INCLUDE_ELEM = 2;
    const REMOVE_INCLUDE_ELEM = 3;

    const NB_LIMITE_FAVORI = 20;
    private EntityManagerInterface $entityManager;
    private Security $security;

    public function __construct(EntityManagerInterface $entityManager, Security $security)
    {
        $this->entityManager = $entityManager;
        $this->security = $security;
    }

    /**
     * @param UserInterface $user
     * @param array $params
     * @param array $exclusions
     * @param $option self::INCLUDE_ALL|self::EXCLUDE_ALL|self::INCLUDE_ELEM|self::REMOVE_INCLUDE_ELEM
     * @return void
     */
    public function saveSearch(UserInterface $user, ?PlaquetteSearch $plaquetteSearch, $exclusion = null, $option = null, $numRequest = null, $selectionSessionId = null)
    {
        $sfRepo = $this->entityManager->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $sf */
        if ($selectionSessionId) {
            $sf = $this->getTemporaryFavoriOrCreateIt($user, $selectionSessionId);
        } else {
            $sf = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);
        }
        $exclusions = $this->calculateExclusion($option, $plaquetteSearch, $user, $sf, $exclusion, $numRequest);

        if (!$sf) {
            $sf = new SelectionFormation($user, [$plaquetteSearch->toArray()], [$exclusions]);
        } else {
            if ($numRequest === null) {
                $sf->replaceLastSearch($plaquetteSearch->toArray(), $exclusions);
            } else {
                $sf->replaceSearchNum($numRequest, $plaquetteSearch ? $plaquetteSearch->toArray() : null, $exclusions);
                if ($option == self::REMOVE_INCLUDE_ELEM) {
                    $sf->completeOtherRequestsExclusions($numRequest, $exclusion);
                }
            }
        }
        $this->entityManager->persist($sf);
        $this->entityManager->flush();
    }

    public function reinitSearch(UserInterface $user)
    {
        $sfRepo = $this->entityManager->getRepository(SelectionFormation::class);
        $sfRepo->createQueryBuilder('sf')
            ->delete(SelectionFormation::class, 'sf')
            ->where('sf.person = :person')
            ->andWhere('sf.isFavori = false')
            ->setParameter("person", $user)
            ->getQuery()
            ->execute();
    }

    public function newSearch(UserInterface $user)
    {
        $sfRepo = $this->entityManager->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $sf */
        $sf = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);
        if (!$sf) {
            $sf = new SelectionFormation($user, [], []);
        } else {
            $sf->addSearch([], []);
        }
        $this->entityManager->persist($sf);
        $this->entityManager->flush();
    }

    public function clearCurrentSearch(Person $user)
    {
        $sfRepo = $this->entityManager->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $sf */
        $sf = $sfRepo->findOneBy(['person' => $user, 'isFavori' => false]);
        if (!$sf) {
            $sf = new SelectionFormation($user, [], []);
        } else {
            $sf->clearLastSearch();
        }
        $this->entityManager->persist($sf);
        $this->entityManager->flush();

    }

    /**
     * Fonction qui retourne le tableau des exclusions en fonction de l'action souhaitée ($option)
     * @param int $option
     * @param FormationRepository $sessionRepo
     * @param PlaquetteSearch $plaquetteSearch
     * @param UserInterface $user
     * @param SelectionFormation $sf
     * @param $exclusion
     * @return array|void|null
     */
    public function calculateExclusion(int $option, PlaquetteSearch $plaquetteSearch = null, UserInterface $user, ?SelectionFormation $sf, $exclusion, $numRequest = null)
    {
        /** @var FormationRepository $sessionRepo */
        $sessionRepo = $this->entityManager->getRepository(Formation::class);

        switch ($option) {
            case self::INCLUDE_ALL:
                $exclusions = [];
                break;
            case self::EXCLUDE_ALL:
                $exclusions = $this->getSearch($plaquetteSearch, null, null, $user, false, false, true);
                break;
            case self::INCLUDE_ELEM:
                if (!$sf || ($numRequest !== null && !$sf->getExclusionNum($numRequest)) || ($numRequest === null && !$sf->getLastExclusion())) {
                    $exclusions =  $this->getSearch($plaquetteSearch, null, null, $user, false, false, true);
                    unset($exclusions[array_search($exclusion, $exclusions)]);
                } else {
                    $exclusions = array_filter($numRequest !== null ? $sf->getExclusionNum($numRequest) : $sf->getLastExclusion(), fn($id) => $id != $exclusion);
                }
                break;
            case self::REMOVE_INCLUDE_ELEM:
                if (!$sf) {
                    return;
                } else {
                    $existants = $numRequest !== null ? $sf->getExclusionNum($numRequest) : $sf->getLastExclusion();
                    $exclusions = array_merge($existants, [$exclusion]);
                }
                break;
            default:
                throw new \InvalidArgumentException();
        }
        return $exclusions;
    }

    public function countCurrentSelection(UserInterface $user, $selection = null, bool $isFavori = false) {
        if (!$selection) {
            /** @var SelectionFormation $selection */
            $selection = $user->getCurrentSelectionFormation();
        }
        $formations = array();

        if ($selection) {
            $exclusions = $selection->getExclusions();
            foreach($selection->getSearchFields() as $key => $searchFields) {
                $searchFromSelection = new PlaquetteSearch();
                $searchFields = $this->getFieldsWithoutDate($isFavori, $searchFields);
                $searchFromSelection->prepareSearch($searchFields, $this->getAllExclusions($exclusions[$key]));
                $results = $this->getSearch($searchFromSelection, null, null, $user, true);
                $array =  array_map(function($r){
                    return $r["id"];
                }, $results);

                $formations = array_unique(array_merge($formations, $array));
            }
        }
        return count($formations);
    }

    /**
     * Charge les sessions selectionnées par les requetes dans le SessionFormation
     *
     * @param Person $user
     * @param SelectionFormation|null $selectionFormation
     * @return array
     */
    public function loadSessionsInSelection(Person $user, ?SelectionFormation $selectionFormation = null, $forProgramme = false, $favori = false) {
        $formations = array();
        $programmes = array();
        if ($selectionFormation) {
            foreach($selectionFormation->getSearchFields() as $key => $searchFields) {
                $searchFields = $this->getFieldsWithoutDate($favori, $searchFields);
                $exclusions = $selectionFormation->getExclusions()[$key];
                $searchFromSelection = new PlaquetteSearch();
                if ($favori && !$forProgramme) {
                    $searchFromSelection->prepareSearch($searchFields);
                } else {
                    $searchFromSelection->prepareSearch($searchFields, $this->getAllExclusions($exclusions));
                }
                $results = $this->getSearch($searchFromSelection, null, null, $user);
                $formations[$key] = [];
                foreach ($results as $result) {
                    if ($forProgramme) {
                        array_push($programmes, $result->getId());
                    } else {
                        $tmp = $result->toArrayPlaquette($exclusions);
                        $tmp['userHasAccess'] = $this->security->isGranted('view', $result);
                        array_push($formations[$key], $tmp);
                    }
                }
            }
        }
        return $forProgramme ? array_unique($programmes) : $formations;
    }

    /**
     * @param Person $user
     * @return array
     */
    public function canAddFavori(Person $user, $ignoreCurrentSelectionFormation = false): array
    {
        $error = null;
        $codeError = null;
        if (!$ignoreCurrentSelectionFormation) {
            if (!$user->getCurrentSelectionFormation()) {
                $error = 'Aucune selection en cours';
                $codeError = 404;
            }
        }
        if (count($user->getSelectionFormationsFavori()) >= self::NB_LIMITE_FAVORI) {
            $error = 'Vous avez atteint la limite du nombre de favori. Merci d\'en supprimer pour en ajouter à nouveau';
            $codeError = 403;
        }
        return array($error, $codeError);
    }

    public function getProgrammes(Person $user, $favori): array
    {
        if ($favori) {
            $temporaryFavori = $user->getTemporaryFavori();
            $favori = $temporaryFavori ? $temporaryFavori->getId() : $favori;
        }

        $formationsRepository = $this->entityManager->getRepository(Formation::class);
        $selection = $favori != 0 ? $user->getFavoriSelectionFormation($favori) : $user->getCurrentSelectionFormation();
        $sessions = $formationsRepository->findPlaquetteProgrammes($this->loadSessionsInSelection($user, $selection, true, $favori));
        $indemnisations = [];

        foreach ($sessions as $session) {
            $indemnisations = $this->calculateIndemnisation($session, $indemnisations);
        }

        $sortedSessions = [];
        foreach($sessions as $session) {
            $sortedSessions[$session->getProgramme()->getPresence()][] = $session;
        }

        $specialitiesAndCategories = $this->getSpecAndCategFromSelection($selection);

        return ["sessions" => $sortedSessions, "indemnisations" => $indemnisations, "professions" => $specialitiesAndCategories["professions"], "specialites" => $specialitiesAndCategories["specialites"]];
    }

    public function getSpecAndCategFromSelection($selection) {
        $professions = [];
        $specialites = [];
        foreach ($selection->getSearchFields() as $search) {
            if ($search["specialities"]) {
                foreach($search["specialities"] as $specialite) {
                    array_push($specialites, $specialite);
                }
            } elseif ($search["categories"]) {
                foreach($search["categories"] as $categorie) {
                    array_push($professions, $categorie);
                }
            }
        }
        $professions = array_unique($professions);
        $specialites = array_unique($specialites);

        return ["professions" => $professions, "specialites" => $specialites];
    }

    public function calculateIndemnisation($formation, $indemnisations = [])
    {
        $thereIsAPrice = false;
        $indemnisationsRepository = $this->entityManager->getRepository(Indemnisation::class);
        $groupesList = ProgrammeType::getGroupes();
        $groupes = [];
        if (is_array($formation->getProgramme()->getCategories()) || is_object($formation->getProgramme()->getCategories()))
        {
            foreach ($formation->getProgramme()->getCategories() as $categorie) {
                if ($formation->getProgramme()->getSpecialities()) {
                    foreach($formation->getProgramme()->getSpecialities() as $specialite) {
                        if (isset($groupesList[$categorie][$specialite]) && !in_array($groupesList[$categorie][$specialite], $groupes)) {
                            $groupes[] = $groupesList[$categorie][$specialite];
                        }
                    }
                }
            }
        }

        $allIndemnisations = $indemnisationsRepository->findBy(array(
            "format" => Programme::FORMATS_RAW[$formation->getProgramme()->getFormat()],
            "presence" => $formation->getProgramme()->getPresence(),
            "formType" => $formation->getProgramme()->getFormType(),
            "year" => $formation->getProgramme()->getYear(),
        ));

        foreach ($formation->getProgramme()->getPrisesEnCharge() as $priseEnCharge) {
            $indemnisationGroups = array();
            foreach ($allIndemnisations as $indemnisation) {
                if (in_array($indemnisation->getGroup(), $groupes)) {
                    $p = $indemnisation->getPriseEnCharges()->filter(function(IndemnisationPriseEnCharge $indemnisationPriseEnCharge) use ($priseEnCharge, $indemnisation, $formation) {
                        return $indemnisationPriseEnCharge->getPriseEnCharge() == $priseEnCharge && $indemnisation->getNbHours() == $formation->getProgramme()->getDurationTotal() && $indemnisationPriseEnCharge->getPrice() != null;
                    })->first();
                    if ($p) {
                        $indemnisationGroups[$indemnisation->getGroup()] = $p->getIndemnisation()->getIndemnisationParticipant();
                        $thereIsAPrice = true;
                    }
                }
            }
            if (count($indemnisationGroups)) {
                if (!isset($indemnisations[$formation->getId()])) {
                    $indemnisations[$formation->getId()] = [];
                }
                array_push($indemnisations[$formation->getId()], $indemnisationGroups);
            }
        }

        foreach($groupes as $group) {
            if(!isset($indemnisations[$formation->getId()][0][$group])) {
                $indemnisations[$formation->getId()][0][$group] = null;
            }
        }
        $indemnisations[$formation->getId()]["thereIsAPrice"] = $thereIsAPrice;
        return $indemnisations;
    }

    /**
     * Fonction qui retourne la Selection temporaire ou le crée en vue d'une modification temporaire
     * @param Person $user
     * @param $selectionSessionId
     * @return SelectionFormation
     */
    public function getTemporaryFavoriOrCreateIt(Person $user, $selectionSessionId) : SelectionFormation
    {
        $sfRepo = $this->entityManager->getRepository(SelectionFormation::class);
        /** @var SelectionFormation $parentSf */
        $parentSf = $sfRepo->findOneBy(['person' => $user, 'id' => $selectionSessionId]);
        $sf = $sfRepo->findOneBy(['person' => $user, 'parentFavori' => $parentSf, 'isTemporary' => true]);

        if (!$sf) {
            $sf = new SelectionFormation($user, $parentSf->getSearchFields(), $parentSf->getExclusions());
            $sf->cloneInfoParentFavori($parentSf);
        }
        return $sf;
    }

    public function getSearch($search, $page, $nbPerPage, Person $user = null, $forCountSelection = false, $count = false, $justIds = false) {
        $formationsRepository = $this->entityManager->getRepository(Formation::class);
        $elearningTakeAccount = [];
        if (!$search->presence || ($search->presence && $search->presence == Programme::PRESENCE_ELEARNING)) {
            $searchElearningResults = $formationsRepository->createPlaquetteSearchResultsQueryBuilder($search, $user, true)->getQuery()->getResult();
            foreach($searchElearningResults as $elearning) {
                $elearningTakeAccount[$elearning->getProgramme()->getId()] = isset($elearningTakeAccount[$elearning->getProgramme()->getId()]) ? $elearningTakeAccount[$elearning->getProgramme()->getId()] : $elearning;
            }
        }

        $seachResult = $search->presence == Programme::PRESENCE_ELEARNING ? [] : $formationsRepository->createPlaquetteSearchResultsQueryBuilder($search, $user, false)->getQuery()->getResult();

        foreach($elearningTakeAccount as $elearning) {
            array_push($seachResult, $elearning);
        }

        $ids =  array_map(function(Formation $f){
            return $f->getId();
        }, $seachResult);

        if ($justIds) {
             return $ids;
        }

        return $formationsRepository->findPlaquetteSearchResultsByIds($page, $nbPerPage, $forCountSelection, $ids, $count);
    }

    // Fonction qui permet de completer la liste des exlusions par les formations elearning soeurs d'une formation exclue
    public function getAllExclusions($exclusions)
    {
        $formationsRepository = $this->entityManager->getRepository(Formation::class);
        $elearnings = $formationsRepository->findElearnings($exclusions);
        if ($elearnings) {
            $programmes = array_unique(array_map(function(Formation $f) {
                return $f->getProgramme()->getId();
            }, $elearnings));
            $elearningSons = $formationsRepository->findProgrammesSons($programmes);
            $elearningSisters = array_map(function($e) {
                return $e["id"];
            }, $elearningSons);
            return array_merge($elearningSisters, $exclusions);
        }
        return $exclusions;
    }

    public function getFieldsWithoutDate(bool $favori, array $searchFields): array
    {
        if ($favori) {
            if (isset($searchFields["start"])) {
                unset($searchFields['start']);
            }
            if (isset($searchFields["end"])) {
                unset($searchFields['end']);
            }
        }
        return $searchFields;
    }
}
