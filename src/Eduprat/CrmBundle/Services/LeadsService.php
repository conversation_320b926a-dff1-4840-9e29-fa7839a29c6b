<?php

namespace Eduprat\CrmBundle\Services;

use Doctrine\ORM\EntityManagerInterface;
use Eduprat\AdminBundle\Entity\Person;
use Eduprat\AdminBundle\Repository\PersonRepository;
use Eduprat\AdminBundle\Services\CsvParser;
use Eduprat\DomainBundle\Entity\Participant;
use Eduprat\DomainBundle\Entity\LeadHistory;
use Eduprat\DomainBundle\Services\AdressesService;
use Eduprat\DomainBundle\Services\EmailSender;
use Symfony\Component\Validator\Validator\ValidatorInterface;

class LeadsService
{
    public const PART_IMPORTABLES = array(
        Participant::PART_GPM,
        Participant::PART_MSOIGNER,
        Participant::PART_MFM,
        Participant::PART_PODOLOGUE,
        Participant::PART_SITE_INTERNET
    );

    public const FIELD_ADVISOR = "conseiller";
    public const FIELD_ADHERENT = "n-adherent";
    public const FIELD_CIVILITY = "civilite";
    public const FIELD_FIRSTNAME = "prenom";
    public const FIELD_LASTNAME = "nom";
    public const FIELD_BIRTHDATE = "date-naissance";
    public const FIELD_DATECREATION = "date-creation-rdv";
    public const FIELD_DATEECHEANCE = "date-echeance-rdv";
    public const FIELD_HEUREECHEANCE = "heure-echeance-rdv";
    public const FIELD_COMMENT = "commentaire-rdv";
    public const FIELD_CATEGORY = "profession";
    public const FIELD_SPECIALITY = "specialite";
    public const FIELD_EXERCICEMODE = "mode-exercice";
    public const FIELD_ADDRESS = "adresse";
    public const FIELD_ZIPCODE = "code-postal";
    public const FIELD_REGION = "region";
    public const FIELD_CITY = "ville";
    public const FIELD_PHONE = "tel-autre";
    public const FIELD_MOBILE = "tel-portable";
    public const FIELD_EMAIL = "email";
    public const FIELD_CODE_APPORTEUR = "code-apporteur";
    public const FIELD_RPPS = "rpps";

    protected PersonRepository $personRepository;
    protected ?Person $leadMfmReferent = null;
    protected ?Person $leadPodologueReferent = null;

    public function __construct(
        private readonly EntityManagerInterface $em,
        private readonly EmailSender            $emailSender,
        private readonly ValidatorInterface     $validator,
        private readonly AdressesService        $adressesService,
        private readonly CsvParser              $csvParser,
        private readonly string                 $leadMfmReferentId,
        private readonly string                 $leadPodologueReferentId,
    )
    {
        $this->personRepository = $this->em->getRepository(Person::class);
    }

    public function previewLeads(string $partner, array $datas, $geocode = false, $submit = false): array
    {
        $participantRepository = $this->em->getRepository(Participant::class);
        $updated = [];
        $created = [];
        $errors = [];

        foreach($datas as $data) {

            $new = false;
            $data = $this->sanitizeRow($data);

            $index = md5($data[self::FIELD_FIRSTNAME] . $data[self::FIELD_LASTNAME]);


            $participantErrors = array();

            if ($data === false) {
                continue;
            }

            $participants = $participantRepository->findForLeadsImport($data);

            if(count($participants) === 0) {
                $new = true;
                $participants = [new Participant()];
            }

            foreach ($participants as &$participant) {
                if(!$submit) {
                    $participant = $this->hydrateLead($partner, $participant, $data, $geocode);

                    $validationErrors = $this->validator->validate($participant);

                    if (isset($data["errors"])) {
                        foreach ($data["errors"] as $error) {
                            $participantErrors[] = array(
                                'error' => "admin.formation.import.error_validation",
                                'message' => $error,
                                'participant' => $data
                            );
                        }
                    }

                    if ($validationErrors->count() > 0) {
                        foreach ($validationErrors as $error) {
                            $participantErrors[] = array(
                                'error' => "admin.formation.import.error_validation",
                                'message' => $validationErrors->get(0)->getMessage(),
                                'participant' => $data
                            );
                        }
                    }

                    $errors = array_merge($errors, $participantErrors);
                }

                if (count($participantErrors) > 0) {
                    $end = false;
                    foreach($participantErrors as $participantError) {
                        if(!str_contains($participantError["message"], "Catégorie Professionnelle :") && !str_contains($participantError["message"], "Spécialité :")) {
                            $end = true;
                        }
                    }
                    // Si au moins une erreur différente de malformation categ pro ou spécialité, alors on stop
                    if($end) {
                        continue 2;
                    }
                }
            }
            unset($participant);

            if ($new) {
                $created[$index]["new"] = true;
                $created[$index]["participants"] = $participants;
                if(!$submit) {
                    $created[$index]["data"] = $this->hydrateLead($partner, new Participant(), $data, $geocode);
                }
            } else {
                $updated[$index]["new"] = false;
                $updated[$index]["participants"] = $participants;
                if(!$submit) {
                    $updated[$index]["data"] = $this->hydrateLead($partner, new Participant(), $data, $geocode);
                }
            }
        }

        return array(
            "updated" => $updated,
            "created" => $created,
            "errors"  => $errors,
        );
    }

    public function sanitizeRow(array $data): false|array
    {
        $sanitizedData = array();
        foreach ($data as $key => $value) {
            if($key === self::FIELD_PHONE || $key === self::FIELD_ADHERENT || $key === self::FIELD_MOBILE) {
                if(trim($value) === "") {
                    $sanitizedData[$key] = null;
                }
                else {
                    $sanitizedData[$key] = $value;
                }
            }
            else {
                if(trim($value) === "") {
                    $sanitizedData[$key] = null;
                }
                else {
                    $sanitizedData[$key] = trim($value);
                }
            }
        }

        $dateErrorString = "le format de la date n'est pas valide, format à respecter : jj/mm/aaaa (ex: 31/12/2021)";
        if (isset($sanitizedData[self::FIELD_DATECREATION])) {
            $dateTime = \DateTime::createFromFormat('d/m/Y', substr($sanitizedData[self::FIELD_DATECREATION], 0, 10));
            if ($dateTime === false) {
                $sanitizedData["errors"][] = "Date création RDV : . $dateErrorString";
            }
            $sanitizedData[self::FIELD_DATECREATION] = $dateTime ? $dateTime->setTime(0, 0, 0) : null;
        }

        if (!array_filter($sanitizedData)) {
            return false;
        }

        return $sanitizedData;
    }

    public function hydrateLead(string $partner, Participant $participant, $data, $geocode = false): Participant
    {
        // Champs spécifiques leads
        $participant
            ->setLeadType($partner)
            ->setPartenariat($partner)
            ->setLeadStatus(Participant::LEAD_ON_VALUE)
            ->setLeadCreationDate($data[self::FIELD_DATECREATION] ?? new \DateTime())
            ->setLeadContactDate(trim(sprintf("%s %s", $data[self::FIELD_DATEECHEANCE] ?? "", $data[self::FIELD_HEUREECHEANCE] ?? "")))
            ->setLeadComment($data[self::FIELD_COMMENT] ?? null)
            ->setGpmMemberNumber($data[self::FIELD_ADHERENT] ?? null)
            ->setCodeApporteur($data[self::FIELD_CODE_APPORTEUR] ?? null)
            ->setBirthDate($data[self::FIELD_BIRTHDATE] && !$participant->getBirthDate() ? \DateTime::createFromFormat('d/m/Y', $data[self::FIELD_BIRTHDATE]) : $participant->getBirthDate())
            ->setEmail($data[self::FIELD_EMAIL] && !$participant->getEmail() ? $data[self::FIELD_EMAIL] : $participant->getEmail());
        ;

        // On cherche le conseiller dans la bdd des utilisateurs extranet
        if ($data[self::FIELD_ADVISOR]) {
            $advisor = $this->personRepository->findAdvisorByFullname($data[self::FIELD_ADVISOR]);
            if ($advisor) {
                $participant->setAdvisor($advisor);
            }
        }

        if ($participant->getId() === null) {
            // Les nouveaux participants sont directement des prospects
            $participant->setIsProspect(true);
            // Remplissage des attributs surchargeables
            if (!$participant->getFirstname()) {
                $participant->setFirstname($data[self::FIELD_FIRSTNAME] ?? null);
            }
            if (!$participant->getLastname()) {
                $participant->setLastname($data[self::FIELD_LASTNAME] ?? null);
            }
            if (!$participant->getCivility()) {
                $participant->setCivility($data[self::FIELD_CIVILITY] ?? null);
            }
            if (!$participant->getCategory()) {
                $participant->setCategory($data[self::FIELD_CATEGORY] ?? null);
            }
            if (!$participant->getSpeciality()) {
                $participant->setSpeciality($data[self::FIELD_SPECIALITY] ?? null);
            }
            if (!$participant->getRegion()) {
                $participant->setRegion($data[self::FIELD_REGION] ?? null);
            }
            if (!$participant->getRpps()) {
                $participant->setRpps($data[self::FIELD_RPPS] ?? null);
            }
        }



        $participant->setAddress($data[self::FIELD_ADDRESS] ?? $participant->getAddress());
        $zipCode = null;
        if(isset($data[self::FIELD_ZIPCODE])) {
            $zipCode = strlen($data[self::FIELD_ZIPCODE]) == 4 ? "0" . $data[self::FIELD_ZIPCODE] : $data[self::FIELD_ZIPCODE];
        }
        $participant->setZipCode($zipCode ?? $participant->getZipCode());
        $participant->setCity($data[self::FIELD_CITY] ?? $participant->getCity());
        $participant->setRegion($data[self::FIELD_REGION] ?? $participant->getRegion());
        $participant->setPhone($data[self::FIELD_PHONE] ?? $participant->getPhone());
        $participant->setMobile($data[self::FIELD_MOBILE] ?? $participant->getMobile());

        // Si prospect, on surcharge email des données du csv
        // if( $participant->isProspect()) {
        //     $participant->setEmail($data[self::FIELD_EMAIL] ?? $participant->getEmail());
        // }

        // Remplissage des attributs du participant si non existants
        if (!$participant->getExerciceMode()) {
            $participant->setExerciceMode($data[self::FIELD_EXERCICEMODE] ?? null);
        }
        if(!$participant->getUga()) {
            if ($geocode === true) {
                try {
                    $address = sprintf("%s %s %s", $participant->getAddress(), ltrim($participant->getZipCode(), '0'), $participant->getCity());
                    if (!empty($address)) {
                        $result = $this->adressesService->getGeocodeAdresse($address);
                        if (count($result["adresse"])) {
                            $coordonnees = explode(",", $result["adresse"][0]["coordonnees"]);
                            $participant->setLatitude($coordonnees[0]);
                            $participant->setLongitude($coordonnees[1]);
                        }
                        $uga = $this->adressesService->getAddressUGA($participant->getAddress(), $participant->getZipCode(), $participant->getCity(), !is_null($participant->getLatitude()) ? array(
                            $participant->getLongitude(),
                            $participant->getLatitude(),
                        ) : null);
                        if (!is_null($uga)) {
                            $participant->setUga($uga["id"]);
                        }
                    }
                } catch (\Exception $exception) {}
            }

        }

        $ugaCoordinators = array();
        if ($participant->getUga()) {
            $ugaCoordinators = $this->personRepository->findCoordinatorsByUga($participant->getUga());
        }

        /**
         * Association des coordinateurs / référents LEAD :
         * GPM :
         *   Coordinateur = Referent = En fonction de l'uga ou par defaut Stéphanie Dreano
         * Union des podologues :
         *   Caroline Pareja est associé en tant que Referent dans tous les cas
         *   Coordinateur conservé si existant ou associé en fonction de l'uga ou par défaut Caroline Pareja
         * M Soigner / MFM :
         *   Stephanie Dreano associé en tant que CR et Referent dans tous les cas
         */
        if ($participant->isLeadGpm()) {
            if (count($ugaCoordinators) === 1) {
                $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : $ugaCoordinators[0]);
                $participant->setCoordinator($ugaCoordinators[0]);
            } else {
                $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : $this->getReferentMfm());
                $participant->setCoordinator($this->getReferentMfm());
            }
        } else if ($participant->isLeadPodologue()) {
            if ($participant->getCoordinator() === null || $participant->isProspect()) {
                if (count($ugaCoordinators) === 1) {
                    $participant->setCoordinator($ugaCoordinators[0]);
                } else {
                    $participant->setCoordinator($this->getReferentPodologue());
                }
            }
            $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : $this->getReferentPodologue());
        } else if ($participant->isLeadMfm() || $participant->isLeadMSoigner()) {
            $coordinator = $this->getReferentMfm();
            $participant->setCoordinator($coordinator);
            $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : $coordinator);
        } else if ($participant->isLeadSiteInternet()) {
            if (count($ugaCoordinators) === 1) {
                $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : $ugaCoordinators[0]);
                $participant->setCoordinator($ugaCoordinators[0]);
            } else {
                $participant->setLeadReferent($participant->getLeadReferent() ? $participant->getLeadReferent() : null);
                $participant->setCoordinator($participant->getCoordinator() ? $participant->getCoordinator() : null);
            }
        }
        return $participant;
    }

    public function importLeads(string $partner, array $formData, array $csvDatas): false|array
    {

        $preview = $this->previewLeads($partner, $csvDatas, true, true);

        $rppsList = [];
        foreach($preview["updated"] as $item) {
            foreach($item["participants"] as $participant) {
                if($participant->getRpps()) {
                    $rppsList[$participant->getRpps()] = $participant;
                }
            }
        }

        $imported = array_merge($preview["created"], $preview["updated"]);

        $dataArray = [];
        foreach($csvDatas as $data) {

            $new = false;
            $data = $this->sanitizeRow($data);
            $index = md5($data[self::FIELD_FIRSTNAME] . $data[self::FIELD_LASTNAME]);

            $dataArray[$index] = $data;
        }

        $emailsToSend = array();
        $participantErrors = array();
        if (isset($formData["lead"])) {
            foreach ($formData["lead"] as $index => $item) {
                if (isset($imported[$index])) {

                    // radio bouton "no_import" = on passe au suivant
                    if ($item["import"] === "no_import") {
                        continue;
                    }

                    // participant à surcharger = id dans "import", si vide = nouveau participant
                    if ($item["import"] !== "") {
                        $participantId = (int) $item["import"];
                        $participant = array_values(array_filter($imported[$index]['participants'], function(Participant $participant) use ($participantId) {
                            return $participant->getId() === $participantId;
                        }))[0];
                        if ($participant->isLead()) {
                            $newLeadHistory = new LeadHistory($participant);
                            $this->em->persist($newLeadHistory);
                        }
                        $participant = $this->hydrateLead($partner, $participant, $dataArray[$index], true);
                        if ($participant->isProspect()) {
                            $participant->setEmail($dataArray[$index][self::FIELD_EMAIL] ?? $participant->getEmail());
                        }
                    } else {
                        // $participant = $imported[$index]['data'];
                        $participant = $this->hydrateLead($partner, new Participant(), $dataArray[$index], true);
                    }

                    /** @var Participant $participant */
                    if ($participant) {
                        $participant->setLeadContactDate($item["leadContactDate"]);
                        $participant->setLeadComment($item["leadComment"]);
                        $participant->setLeadReferent(!empty($item["leadReferent"]) ? $this->personRepository->find($item["leadReferent"]) : null);
                        $participant->setAdvisor(!empty($item["advisor"]) ? $this->personRepository->find($item["advisor"]) : null);
                        if ($participant->getAdvisor()) {
                            $participant->getAdvisor()->setCodeApporteur($participant->getCodeApporteur());
                        }
                        $participant->setCoordinator(!empty($item["leadReferent"]) ? $this->personRepository->find($item["leadReferent"]) : null);
                        $participant->setSpeciality(isset($item["speciality"]) ? $item["speciality"] : $participant->getSpeciality());
                        $participant->setCategory(isset($item["category"]) ? $item["category"] : $participant->getCategory());

                        if ($dataArray[$index]["rpps"] && !$participant->getRpps()) {
                            if (!isset($rppsList[$dataArray[$index]["rpps"]]) || $rppsList[$dataArray[$index]["rpps"]]->getId() == $participant->getId()) {
                                $participant->setRpps($dataArray[$index]["rpps"]);
                            }
                        };
                    }

                    $this->em->persist($participant);
                    if ($participant->getLeadReferent()) {
                        $emailsToSend[$participant->getLeadReferent()->getEmail()] = isset($emailsToSend[$participant->getLeadReferent()->getEmail()]) ? $emailsToSend[$participant->getLeadReferent()->getEmail()] + 1 : 1;
                    }

                    $validationErrors = $this->validator->validate($participant);

                    if ($validationErrors->count() > 0) {
                        foreach ($validationErrors as $error) {
                            $participantErrors[] = array(
                                'error' => "admin.formation.import.error_validation",
                                'message' => $validationErrors->get(0)->getMessage(),
                                'participant' => $participant
                            );
                        }
                    }
                }
            }
        }

        if($participantErrors !=  array()) {
            return $participantErrors;
        }

        $this->em->flush();

        foreach ($emailsToSend as $email => $count) {
            $this->emailSender->sendNewLeadByEmail($email, $count);
        }

        return false;
    }

    public function getCoordinators() {
        return $this->personRepository->findCoordinators();
    }

    public function getAdvisors() {
        return $this->personRepository->findAdvisors();
    }

    public function getReferentMfm(): Person {
        if ($this->leadMfmReferent === null) {
            $this->leadMfmReferent = $this->personRepository->find($this->leadMfmReferentId);
        }
        return $this->leadMfmReferent;
    }

    public function getReferentPodologue(): Person {
        if ($this->leadPodologueReferent === null) {
            $this->leadPodologueReferent = $this->em->getRepository(Person::class)->find($this->leadPodologueReferentId);
        }
        return $this->leadPodologueReferent;
    }

    public function parseCSV(false|string $getRealPath, int $nbLinesIgnored, string $string, true $true): false|array
    {
        return $this->csvParser->parceCSV($getRealPath, $nbLinesIgnored, $string, $true);
    }
}
